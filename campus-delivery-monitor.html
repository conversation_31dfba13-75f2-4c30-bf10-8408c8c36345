<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配送实时监控 - 校园外卖管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            width: 1440px;
        }
        
        .placeholder-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
            background: linear-gradient(135deg, #f759ab 0%, #eb2f96 100%);
            color: white;
        }
        
        .placeholder-icon {
            font-size: 80px;
            margin-bottom: 24px;
            opacity: 0.8;
        }
        
        .placeholder-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .placeholder-description {
            font-size: 18px;
            opacity: 0.8;
            max-width: 600px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="placeholder-content">
        <div class="placeholder-icon">
            <i class="fas fa-satellite-dish"></i>
        </div>
        <h1 class="placeholder-title">配送实时监控</h1>
        <p class="placeholder-description">
            配送实时监控页面提供配送过程的实时追踪，包括配送员位置、订单状态、
            配送路径、预计到达时间等信息。支持地图可视化和异常情况预警。
        </p>
    </div>
</body>
</html>
