<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单详情 - 校园外卖管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #001529 0%, #002140 100%);
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 18px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: #ff6b35;
        }
        
        .menu-item.active {
            color: white;
            background-color: rgba(255, 107, 53, 0.2);
            border-left-color: #ff6b35;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item {
            color: #ff6b35;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            border-radius: 8px;
        }
        
        .header-icon:hover {
            color: #ff6b35;
            background-color: #f5f5f5;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .page-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }
        
        .btn-secondary {
            background: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-danger {
            background: #ff4d4f;
            color: white;
        }
        
        .order-header {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        
        .order-status-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .order-id {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .status-tag {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-processing {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .order-timeline {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .timeline-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }
        
        .timeline-item:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 16px;
            right: -10px;
            width: 20px;
            height: 2px;
            background: #d9d9d9;
        }
        
        .timeline-item.active:not(:last-child)::after {
            background: #ff6b35;
        }
        
        .timeline-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f0f0f0;
            color: #999;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .timeline-item.active .timeline-icon {
            background: #ff6b35;
            color: white;
        }
        
        .timeline-item.completed .timeline-icon {
            background: #52c41a;
            color: white;
        }
        
        .timeline-label {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        
        .timeline-time {
            font-size: 10px;
            color: #999;
            margin-top: 2px;
        }
        
        .order-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .info-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .card-icon {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }
        
        .customer-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .customer-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #666;
        }
        
        .customer-details {
            flex: 1;
        }
        
        .customer-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .customer-phone {
            font-size: 14px;
            color: #666;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .info-label {
            color: #666;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
        }
        
        .merchant-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .merchant-avatar {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #666;
        }
        
        .merchant-details {
            flex: 1;
        }
        
        .merchant-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .merchant-category {
            font-size: 14px;
            color: #666;
        }
        
        .order-items-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            margin-bottom: 24px;
        }
        
        .items-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .item-row {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #fafafa;
            border-radius: 8px;
        }
        
        .item-image {
            width: 48px;
            height: 48px;
            border-radius: 6px;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #999;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .item-specs {
            font-size: 12px;
            color: #999;
        }
        
        .item-quantity {
            font-size: 14px;
            color: #666;
            margin-right: 16px;
        }
        
        .item-price {
            font-size: 16px;
            font-weight: 600;
            color: #ff6b35;
        }
        
        .order-summary {
            background: #fafafa;
            padding: 16px;
            border-radius: 8px;
            margin-top: 16px;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .summary-row.total {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            border-top: 1px solid #e0e0e0;
            padding-top: 8px;
            margin-top: 8px;
        }
        
        .delivery-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        
        .delivery-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .delivery-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #666;
        }
        
        .delivery-details {
            flex: 1;
        }
        
        .delivery-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .delivery-phone {
            font-size: 14px;
            color: #666;
        }
        
        .delivery-status {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: #e6f7ff;
            border-radius: 6px;
            font-size: 14px;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="sidebar-title">校园外卖系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-users menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-truck menu-icon"></i>
                    <span>配送管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-line menu-icon"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bullhorn menu-icon"></i>
                    <span>营销管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="breadcrumb">
                        <span>订单管理</span> / <span>订单列表</span> / <span class="breadcrumb-item">订单详情</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">系统管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">订单详情</h1>
                    <div class="page-actions">
                        <button class="btn btn-secondary">
                            <i class="fas fa-print"></i>
                            打印订单
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-edit"></i>
                            编辑订单
                        </button>
                        <button class="btn btn-danger">
                            <i class="fas fa-times"></i>
                            取消订单
                        </button>
                    </div>
                </div>
                
                <div class="order-header">
                    <div class="order-status-bar">
                        <div class="order-id">订单 #20240518001</div>
                        <div class="status-tag status-processing">制作中</div>
                    </div>
                    
                    <div class="order-timeline">
                        <div class="timeline-item completed">
                            <div class="timeline-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-label">订单确认</div>
                            <div class="timeline-time">12:30</div>
                        </div>
                        <div class="timeline-item completed">
                            <div class="timeline-icon">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="timeline-label">支付完成</div>
                            <div class="timeline-time">12:31</div>
                        </div>
                        <div class="timeline-item active">
                            <div class="timeline-icon">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="timeline-label">商家制作</div>
                            <div class="timeline-time">12:35</div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="timeline-label">配送中</div>
                            <div class="timeline-time">--:--</div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <div class="timeline-label">订单完成</div>
                            <div class="timeline-time">--:--</div>
                        </div>
                    </div>
                </div>
                
                <div class="order-info-grid">
                    <div class="info-card">
                        <div class="card-title">
                            <div class="card-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            客户信息
                        </div>
                        
                        <div class="customer-profile">
                            <div class="customer-avatar">张</div>
                            <div class="customer-details">
                                <div class="customer-name">张同学</div>
                                <div class="customer-phone">138****5678</div>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">学号</span>
                            <span class="info-value">2021001234</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">学院</span>
                            <span class="info-value">计算机学院</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">配送地址</span>
                            <span class="info-value">学生宿舍A栋201</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">备注</span>
                            <span class="info-value">不要辣椒，谢谢</span>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <div class="card-title">
                            <div class="card-icon">
                                <i class="fas fa-store"></i>
                            </div>
                            商家信息
                        </div>
                        
                        <div class="merchant-profile">
                            <div class="merchant-avatar">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="merchant-details">
                                <div class="merchant-name">美味小厨</div>
                                <div class="merchant-category">餐饮美食</div>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">商家电话</span>
                            <span class="info-value">138-1234-5678</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">商家地址</span>
                            <span class="info-value">学生食堂二楼A区</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">预计制作时间</span>
                            <span class="info-value">15分钟</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">商家备注</span>
                            <span class="info-value">已收到订单，正在制作中</span>
                        </div>
                    </div>
                </div>
                
                <div class="order-items-card">
                    <div class="card-title">
                        <div class="card-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        订单商品
                    </div>
                    
                    <div class="items-list">
                        <div class="item-row">
                            <div class="item-image">
                                <i class="fas fa-pepper-hot"></i>
                            </div>
                            <div class="item-details">
                                <div class="item-name">麻辣香锅</div>
                                <div class="item-specs">大份 · 微辣</div>
                            </div>
                            <div class="item-quantity">x1</div>
                            <div class="item-price">¥18.00</div>
                        </div>
                        
                        <div class="item-row">
                            <div class="item-image">
                                <i class="fas fa-bowl-rice"></i>
                            </div>
                            <div class="item-details">
                                <div class="item-name">白米饭</div>
                                <div class="item-specs">标准份</div>
                            </div>
                            <div class="item-quantity">x1</div>
                            <div class="item-price">¥2.00</div>
                        </div>
                        
                        <div class="item-row">
                            <div class="item-image">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <div class="item-details">
                                <div class="item-name">柠檬蜂蜜茶</div>
                                <div class="item-specs">中杯 · 少糖</div>
                            </div>
                            <div class="item-quantity">x1</div>
                            <div class="item-price">¥8.00</div>
                        </div>
                    </div>
                    
                    <div class="order-summary">
                        <div class="summary-row">
                            <span>商品小计</span>
                            <span>¥28.00</span>
                        </div>
                        <div class="summary-row">
                            <span>配送费</span>
                            <span>¥3.00</span>
                        </div>
                        <div class="summary-row">
                            <span>优惠券</span>
                            <span>-¥2.50</span>
                        </div>
                        <div class="summary-row total">
                            <span>订单总计</span>
                            <span>¥28.50</span>
                        </div>
                    </div>
                </div>
                
                <div class="delivery-card">
                    <div class="card-title">
                        <div class="card-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        配送信息
                    </div>
                    
                    <div class="delivery-info">
                        <div class="delivery-avatar">李</div>
                        <div class="delivery-details">
                            <div class="delivery-name">李师傅</div>
                            <div class="delivery-phone">139-8765-4321</div>
                        </div>
                        <div class="delivery-status">
                            <i class="fas fa-clock"></i>
                            等待取餐
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">预计送达时间</span>
                        <span class="info-value">13:15</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">配送距离</span>
                        <span class="info-value">约800米</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">配送备注</span>
                        <span class="info-value">请送到宿舍楼下</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
