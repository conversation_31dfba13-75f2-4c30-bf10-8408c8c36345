<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户列表 - 校园外卖管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #001529 0%, #002140 100%);
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 18px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: #ff6b35;
        }
        
        .menu-item.active {
            color: white;
            background-color: rgba(255, 107, 53, 0.2);
            border-left-color: #ff6b35;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item {
            color: #ff6b35;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            border-radius: 8px;
        }
        
        .header-icon:hover {
            color: #ff6b35;
            background-color: #f5f5f5;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .stats-bar {
            display: flex;
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-item {
            background: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 140px;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }
        
        .stat-icon.total {
            background: linear-gradient(135deg, #1890ff, #096dd9);
        }
        
        .stat-icon.active {
            background: linear-gradient(135deg, #52c41a, #389e0d);
        }
        
        .stat-icon.new {
            background: linear-gradient(135deg, #fa8c16, #d46b08);
        }
        
        .stat-icon.vip {
            background: linear-gradient(135deg, #722ed1, #531dab);
        }
        
        .stat-content {
            display: flex;
            flex-direction: column;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .filter-bar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .filter-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }
        
        .filter-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 160px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 120px;
            background: white;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }
        
        .btn-secondary {
            background: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .table-stats {
            font-size: 14px;
            color: #666;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #333;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .data-table td {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .data-table tr:hover {
            background: #fafafa;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #666;
        }
        
        .profile-details {
            display: flex;
            flex-direction: column;
        }
        
        .profile-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }
        
        .profile-id {
            font-size: 12px;
            color: #999;
        }
        
        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .contact-phone {
            color: #333;
        }
        
        .contact-email {
            font-size: 12px;
            color: #999;
        }
        
        .college-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .college-name {
            color: #333;
            font-weight: 500;
        }
        
        .college-major {
            font-size: 12px;
            color: #999;
        }
        
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-inactive {
            background: #fff1f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        .status-pending {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        .user-level {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .level-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            color: white;
        }
        
        .level-normal .level-icon {
            background: #d9d9d9;
        }
        
        .level-vip .level-icon {
            background: #722ed1;
        }
        
        .level-svip .level-icon {
            background: #fa8c16;
        }
        
        .stats-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .stats-orders {
            color: #333;
            font-weight: 500;
        }
        
        .stats-amount {
            font-size: 12px;
            color: #ff6b35;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }
        
        .action-btn.view {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .action-btn.edit {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .action-btn.disable {
            background: #fff1f0;
            color: #ff4d4f;
        }
        
        .pagination {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }
        
        .pagination-info {
            font-size: 14px;
            color: #666;
        }
        
        .pagination-controls {
            display: flex;
            gap: 8px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .page-btn:hover {
            color: #ff6b35;
            border-color: #ff6b35;
        }
        
        .page-btn.active {
            background: #ff6b35;
            color: white;
            border-color: #ff6b35;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="sidebar-title">校园外卖系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-users menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-truck menu-icon"></i>
                    <span>配送管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-line menu-icon"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bullhorn menu-icon"></i>
                    <span>营销管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="breadcrumb">
                        <span>用户管理</span> / <span class="breadcrumb-item">用户列表</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">系统管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">用户列表</h1>
                </div>
                
                <div class="stats-bar">
                    <div class="stat-item">
                        <div class="stat-icon total">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">12,486</div>
                            <div class="stat-label">总用户数</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon active">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">8,924</div>
                            <div class="stat-label">活跃用户</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon new">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">156</div>
                            <div class="stat-label">本月新增</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon vip">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">1,248</div>
                            <div class="stat-label">VIP用户</div>
                        </div>
                    </div>
                </div>
                
                <div class="filter-bar">
                    <div class="filter-item">
                        <label class="filter-label">用户姓名</label>
                        <input type="text" class="filter-input" placeholder="搜索用户姓名">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">学号</label>
                        <input type="text" class="filter-input" placeholder="搜索学号">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">学院</label>
                        <select class="filter-select">
                            <option>全部学院</option>
                            <option>计算机学院</option>
                            <option>经济学院</option>
                            <option>文学院</option>
                            <option>理学院</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">用户状态</label>
                        <select class="filter-select">
                            <option>全部状态</option>
                            <option>正常</option>
                            <option>待认证</option>
                            <option>已禁用</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" style="margin-top: 20px;">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                    <button class="btn btn-secondary" style="margin-top: 20px;">
                        <i class="fas fa-redo"></i>
                        重置
                    </button>
                </div>
                
                <div class="table-container">
                    <div class="table-header">
                        <div class="table-title">用户列表</div>
                        <div class="table-stats">共 12,486 个用户</div>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>用户信息</th>
                                <th>联系方式</th>
                                <th>学院专业</th>
                                <th>用户等级</th>
                                <th>订单统计</th>
                                <th>状态</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="user-profile">
                                        <div class="profile-avatar">张</div>
                                        <div class="profile-details">
                                            <div class="profile-name">张同学</div>
                                            <div class="profile-id">学号: 2021001234</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="contact-info">
                                        <div class="contact-phone">138****5678</div>
                                        <div class="contact-email"><EMAIL></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="college-info">
                                        <div class="college-name">计算机学院</div>
                                        <div class="college-major">软件工程</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-level level-vip">
                                        <div class="level-icon">V</div>
                                        <span>VIP用户</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="stats-info">
                                        <div class="stats-orders">128单</div>
                                        <div class="stats-amount">消费¥2,456</div>
                                    </div>
                                </td>
                                <td><span class="status-tag status-active">正常</span></td>
                                <td>2021-09-01</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn edit">编辑</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="user-profile">
                                        <div class="profile-avatar">李</div>
                                        <div class="profile-details">
                                            <div class="profile-name">李同学</div>
                                            <div class="profile-id">学号: 2022002345</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="contact-info">
                                        <div class="contact-phone">139****4321</div>
                                        <div class="contact-email"><EMAIL></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="college-info">
                                        <div class="college-name">经济学院</div>
                                        <div class="college-major">国际贸易</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-level level-normal">
                                        <div class="level-icon">N</div>
                                        <span>普通用户</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="stats-info">
                                        <div class="stats-orders">45单</div>
                                        <div class="stats-amount">消费¥856</div>
                                    </div>
                                </td>
                                <td><span class="status-tag status-active">正常</span></td>
                                <td>2022-09-01</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn edit">编辑</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="user-profile">
                                        <div class="profile-avatar">王</div>
                                        <div class="profile-details">
                                            <div class="profile-name">王同学</div>
                                            <div class="profile-id">学号: 2023003456</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="contact-info">
                                        <div class="contact-phone">137****2198</div>
                                        <div class="contact-email"><EMAIL></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="college-info">
                                        <div class="college-name">文学院</div>
                                        <div class="college-major">汉语言文学</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-level level-svip">
                                        <div class="level-icon">S</div>
                                        <span>超级VIP</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="stats-info">
                                        <div class="stats-orders">256单</div>
                                        <div class="stats-amount">消费¥4,128</div>
                                    </div>
                                </td>
                                <td><span class="status-tag status-pending">待认证</span></td>
                                <td>2023-09-01</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn edit">编辑</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="user-profile">
                                        <div class="profile-avatar">赵</div>
                                        <div class="profile-details">
                                            <div class="profile-name">赵同学</div>
                                            <div class="profile-id">学号: 2021004567</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="contact-info">
                                        <div class="contact-phone">136****5678</div>
                                        <div class="contact-email"><EMAIL></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="college-info">
                                        <div class="college-name">理学院</div>
                                        <div class="college-major">数学与应用数学</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-level level-normal">
                                        <div class="level-icon">N</div>
                                        <span>普通用户</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="stats-info">
                                        <div class="stats-orders">12单</div>
                                        <div class="stats-amount">消费¥248</div>
                                    </div>
                                </td>
                                <td><span class="status-tag status-inactive">已禁用</span></td>
                                <td>2021-09-01</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn edit">编辑</button>
                                        <button class="action-btn disable">启用</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination">
                        <div class="pagination-info">
                            显示 1-10 条，共 12,486 条记录
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn">上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">...</button>
                            <button class="page-btn">1249</button>
                            <button class="page-btn">下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
