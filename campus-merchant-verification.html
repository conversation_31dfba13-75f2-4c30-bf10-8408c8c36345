<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家审核 - 校园外卖管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #001529 0%, #002140 100%);
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 18px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: #ff6b35;
        }
        
        .menu-item.active {
            color: white;
            background-color: rgba(255, 107, 53, 0.2);
            border-left-color: #ff6b35;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item {
            color: #ff6b35;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            border-radius: 8px;
        }
        
        .header-icon:hover {
            color: #ff6b35;
            background-color: #f5f5f5;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .stats-bar {
            display: flex;
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-item {
            background: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 160px;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }
        
        .stat-icon.pending {
            background: linear-gradient(135deg, #fa8c16, #d46b08);
        }
        
        .stat-icon.approved {
            background: linear-gradient(135deg, #52c41a, #389e0d);
        }
        
        .stat-icon.rejected {
            background: linear-gradient(135deg, #ff4d4f, #cf1322);
        }
        
        .stat-content {
            display: flex;
            flex-direction: column;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .verification-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .priority-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .priority-high {
            background: #fff1f0;
            color: #ff4d4f;
        }
        
        .priority-normal {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .card-content {
            padding: 24px;
        }
        
        .merchant-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .merchant-avatar {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #666;
        }
        
        .merchant-info {
            flex: 1;
        }
        
        .merchant-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .merchant-meta {
            font-size: 14px;
            color: #666;
        }
        
        .application-time {
            font-size: 12px;
            color: #999;
            margin-top: 4px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .info-section {
            background: #fafafa;
            padding: 16px;
            border-radius: 8px;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-icon {
            width: 16px;
            height: 16px;
            background: #ff6b35;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .info-label {
            color: #666;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
        }
        
        .documents-section {
            margin-bottom: 24px;
        }
        
        .documents-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }
        
        .document-item {
            background: #fafafa;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .document-item:hover {
            background: #f0f0f0;
        }
        
        .document-icon {
            width: 48px;
            height: 48px;
            background: #e6f7ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 20px;
            color: #1890ff;
        }
        
        .document-name {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }
        
        .verification-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-approve {
            background: #52c41a;
            color: white;
        }
        
        .btn-reject {
            background: #ff4d4f;
            color: white;
        }
        
        .btn-secondary {
            background: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .notes-section {
            margin-top: 20px;
        }
        
        .notes-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="sidebar-title">校园外卖系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-users menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-truck menu-icon"></i>
                    <span>配送管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-line menu-icon"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bullhorn menu-icon"></i>
                    <span>营销管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="breadcrumb">
                        <span>商家管理</span> / <span class="breadcrumb-item">商家审核</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">系统管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">商家审核</h1>
                </div>
                
                <div class="stats-bar">
                    <div class="stat-item">
                        <div class="stat-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">12</div>
                            <div class="stat-label">待审核</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon approved">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">156</div>
                            <div class="stat-label">已通过</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon rejected">
                            <i class="fas fa-times"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">8</div>
                            <div class="stat-label">已拒绝</div>
                        </div>
                    </div>
                </div>
                
                <div class="verification-card">
                    <div class="card-header">
                        <div class="card-title">校园便利店 - 入驻申请</div>
                        <div class="priority-tag priority-high">紧急</div>
                    </div>
                    <div class="card-content">
                        <div class="merchant-header">
                            <div class="merchant-avatar">
                                <i class="fas fa-shopping-basket"></i>
                            </div>
                            <div class="merchant-info">
                                <div class="merchant-name">校园便利店</div>
                                <div class="merchant-meta">便利店 · 零食饮料日用品</div>
                                <div class="application-time">申请时间：2024-05-18 14:30</div>
                            </div>
                        </div>
                        
                        <div class="info-grid">
                            <div class="info-section">
                                <div class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    联系信息
                                </div>
                                <div class="info-item">
                                    <span class="info-label">负责人</span>
                                    <span class="info-value">李经理</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">联系电话</span>
                                    <span class="info-value">13987654321</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">邮箱</span>
                                    <span class="info-value"><EMAIL></span>
                                </div>
                            </div>
                            
                            <div class="info-section">
                                <div class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    经营信息
                                </div>
                                <div class="info-item">
                                    <span class="info-label">经营地址</span>
                                    <span class="info-value">学生公寓1楼</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">营业时间</span>
                                    <span class="info-value">07:00-23:00</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">配送范围</span>
                                    <span class="info-value">全校区</span>
                                </div>
                            </div>
                            
                            <div class="info-section">
                                <div class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    费用设置
                                </div>
                                <div class="info-item">
                                    <span class="info-label">配送费</span>
                                    <span class="info-value">¥2.00</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">起送金额</span>
                                    <span class="info-value">¥10.00</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">佣金比例</span>
                                    <span class="info-value">5%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="documents-section">
                            <div class="section-title" style="margin-bottom: 16px;">
                                <div class="section-icon">
                                    <i class="fas fa-file"></i>
                                </div>
                                资质文件
                            </div>
                            <div class="documents-grid">
                                <div class="document-item">
                                    <div class="document-icon">
                                        <i class="fas fa-id-card"></i>
                                    </div>
                                    <div class="document-name">营业执照</div>
                                </div>
                                <div class="document-item">
                                    <div class="document-icon">
                                        <i class="fas fa-certificate"></i>
                                    </div>
                                    <div class="document-name">食品经营许可证</div>
                                </div>
                                <div class="document-item">
                                    <div class="document-icon">
                                        <i class="fas fa-user-tie"></i>
                                    </div>
                                    <div class="document-name">法人身份证</div>
                                </div>
                                <div class="document-item">
                                    <div class="document-icon">
                                        <i class="fas fa-store"></i>
                                    </div>
                                    <div class="document-name">店铺照片</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="notes-section">
                            <div class="section-title" style="margin-bottom: 12px;">
                                <div class="section-icon">
                                    <i class="fas fa-comment"></i>
                                </div>
                                审核备注
                            </div>
                            <textarea class="notes-textarea" placeholder="请输入审核意见或备注信息..."></textarea>
                        </div>
                        
                        <div class="verification-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-eye"></i>
                                查看详情
                            </button>
                            <button class="btn btn-reject">
                                <i class="fas fa-times"></i>
                                拒绝申请
                            </button>
                            <button class="btn btn-approve">
                                <i class="fas fa-check"></i>
                                通过审核
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="verification-card">
                    <div class="card-header">
                        <div class="card-title">新鲜果园 - 入驻申请</div>
                        <div class="priority-tag priority-normal">普通</div>
                    </div>
                    <div class="card-content">
                        <div class="merchant-header">
                            <div class="merchant-avatar">
                                <i class="fas fa-apple-alt"></i>
                            </div>
                            <div class="merchant-info">
                                <div class="merchant-name">新鲜果园</div>
                                <div class="merchant-meta">水果生鲜 · 新鲜水果蔬菜</div>
                                <div class="application-time">申请时间：2024-05-17 16:45</div>
                            </div>
                        </div>
                        
                        <div class="info-grid">
                            <div class="info-section">
                                <div class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    联系信息
                                </div>
                                <div class="info-item">
                                    <span class="info-label">负责人</span>
                                    <span class="info-value">王阿姨</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">联系电话</span>
                                    <span class="info-value">13765432198</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">邮箱</span>
                                    <span class="info-value"><EMAIL></span>
                                </div>
                            </div>
                            
                            <div class="info-section">
                                <div class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    经营信息
                                </div>
                                <div class="info-item">
                                    <span class="info-label">经营地址</span>
                                    <span class="info-value">校门口商业街</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">营业时间</span>
                                    <span class="info-value">08:00-20:00</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">配送范围</span>
                                    <span class="info-value">全校区</span>
                                </div>
                            </div>
                            
                            <div class="info-section">
                                <div class="section-title">
                                    <div class="section-icon">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    费用设置
                                </div>
                                <div class="info-item">
                                    <span class="info-label">配送费</span>
                                    <span class="info-value">¥3.00</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">起送金额</span>
                                    <span class="info-value">¥15.00</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">佣金比例</span>
                                    <span class="info-value">6%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="documents-section">
                            <div class="section-title" style="margin-bottom: 16px;">
                                <div class="section-icon">
                                    <i class="fas fa-file"></i>
                                </div>
                                资质文件
                            </div>
                            <div class="documents-grid">
                                <div class="document-item">
                                    <div class="document-icon">
                                        <i class="fas fa-id-card"></i>
                                    </div>
                                    <div class="document-name">营业执照</div>
                                </div>
                                <div class="document-item">
                                    <div class="document-icon">
                                        <i class="fas fa-certificate"></i>
                                    </div>
                                    <div class="document-name">食品经营许可证</div>
                                </div>
                                <div class="document-item">
                                    <div class="document-icon">
                                        <i class="fas fa-user-tie"></i>
                                    </div>
                                    <div class="document-name">法人身份证</div>
                                </div>
                                <div class="document-item">
                                    <div class="document-icon">
                                        <i class="fas fa-store"></i>
                                    </div>
                                    <div class="document-name">店铺照片</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="notes-section">
                            <div class="section-title" style="margin-bottom: 12px;">
                                <div class="section-icon">
                                    <i class="fas fa-comment"></i>
                                </div>
                                审核备注
                            </div>
                            <textarea class="notes-textarea" placeholder="请输入审核意见或备注信息..."></textarea>
                        </div>
                        
                        <div class="verification-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-eye"></i>
                                查看详情
                            </button>
                            <button class="btn btn-reject">
                                <i class="fas fa-times"></i>
                                拒绝申请
                            </button>
                            <button class="btn btn-approve">
                                <i class="fas fa-check"></i>
                                通过审核
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
