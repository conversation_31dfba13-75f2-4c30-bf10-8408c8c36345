<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易流水 - 校园外卖管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            width: 1440px;
        }
        
        .placeholder-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
            background: linear-gradient(135deg, #36cfc9 0%, #13c2c2 100%);
            color: white;
        }
        
        .placeholder-icon {
            font-size: 80px;
            margin-bottom: 24px;
            opacity: 0.8;
        }
        
        .placeholder-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .placeholder-description {
            font-size: 18px;
            opacity: 0.8;
            max-width: 600px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="placeholder-content">
        <div class="placeholder-icon">
            <i class="fas fa-receipt"></i>
        </div>
        <h1 class="placeholder-title">交易流水</h1>
        <p class="placeholder-description">
            交易流水页面记录所有的资金流动，包括订单支付、退款、佣金结算、
            配送费用等详细记录。支持多维度查询和财务对账功能。
        </p>
    </div>
</body>
</html>
