<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单状态管理 - 校园外卖管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #001529 0%, #002140 100%);
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 18px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: #ff6b35;
        }
        
        .menu-item.active {
            color: white;
            background-color: rgba(255, 107, 53, 0.2);
            border-left-color: #ff6b35;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item {
            color: #ff6b35;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            border-radius: 8px;
        }
        
        .header-icon:hover {
            color: #ff6b35;
            background-color: #f5f5f5;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .status-board {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .status-column {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            min-height: 600px;
        }
        
        .column-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .column-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .column-count {
            background: #f0f0f0;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .pending .column-header {
            border-bottom-color: #fa8c16;
        }
        
        .pending .column-count {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .processing .column-header {
            border-bottom-color: #1890ff;
        }
        
        .processing .column-count {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .delivering .column-header {
            border-bottom-color: #722ed1;
        }
        
        .delivering .column-count {
            background: #f0f5ff;
            color: #722ed1;
        }
        
        .completed .column-header {
            border-bottom-color: #52c41a;
        }
        
        .completed .column-count {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .cancelled .column-header {
            border-bottom-color: #ff4d4f;
        }
        
        .cancelled .column-count {
            background: #fff1f0;
            color: #ff4d4f;
        }
        
        .order-card {
            background: #fafafa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 4px solid transparent;
        }
        
        .order-card:hover {
            background: #f5f5f5;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .pending .order-card {
            border-left-color: #fa8c16;
        }
        
        .processing .order-card {
            border-left-color: #1890ff;
        }
        
        .delivering .order-card {
            border-left-color: #722ed1;
        }
        
        .completed .order-card {
            border-left-color: #52c41a;
        }
        
        .cancelled .order-card {
            border-left-color: #ff4d4f;
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .order-id {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        
        .order-time {
            font-size: 12px;
            color: #999;
        }
        
        .order-customer {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .order-merchant {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .order-amount {
            font-size: 16px;
            font-weight: 600;
            color: #ff6b35;
            text-align: right;
        }
        
        .order-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }
        
        .action-btn {
            flex: 1;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            text-align: center;
        }
        
        .action-btn.primary {
            background: #ff6b35;
            color: white;
        }
        
        .action-btn.secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .action-btn:hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="sidebar-title">校园外卖系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-users menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-truck menu-icon"></i>
                    <span>配送管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-line menu-icon"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bullhorn menu-icon"></i>
                    <span>营销管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="breadcrumb">
                        <span>订单管理</span> / <span class="breadcrumb-item">订单状态管理</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">系统管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">订单状态管理</h1>
                </div>
                
                <div class="status-board">
                    <div class="status-column pending">
                        <div class="column-header">
                            <div class="column-title">待处理</div>
                            <div class="column-count">42</div>
                        </div>
                        
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-id">#20240518001</div>
                                <div class="order-time">12:30</div>
                            </div>
                            <div class="order-customer">张同学</div>
                            <div class="order-merchant">美味小厨</div>
                            <div class="order-amount">¥28.50</div>
                            <div class="order-actions">
                                <button class="action-btn primary">接单</button>
                                <button class="action-btn secondary">拒绝</button>
                            </div>
                        </div>
                        
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-id">#20240518004</div>
                                <div class="order-time">10:55</div>
                            </div>
                            <div class="order-customer">赵同学</div>
                            <div class="order-merchant">星空奶茶</div>
                            <div class="order-amount">¥18.00</div>
                            <div class="order-actions">
                                <button class="action-btn primary">接单</button>
                                <button class="action-btn secondary">拒绝</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="status-column processing">
                        <div class="column-header">
                            <div class="column-title">制作中</div>
                            <div class="column-count">128</div>
                        </div>
                        
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-id">#20240518002</div>
                                <div class="order-time">11:45</div>
                            </div>
                            <div class="order-customer">李同学</div>
                            <div class="order-merchant">校园便利店</div>
                            <div class="order-amount">¥15.80</div>
                            <div class="order-actions">
                                <button class="action-btn primary">完成制作</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="status-column delivering">
                        <div class="column-header">
                            <div class="column-title">配送中</div>
                            <div class="column-count">86</div>
                        </div>
                        
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-id">#20240518003</div>
                                <div class="order-time">11:20</div>
                            </div>
                            <div class="order-customer">王同学</div>
                            <div class="order-merchant">新鲜果园</div>
                            <div class="order-amount">¥22.00</div>
                            <div class="order-actions">
                                <button class="action-btn primary">确认送达</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="status-column completed">
                        <div class="column-header">
                            <div class="column-title">已完成</div>
                            <div class="column-count">2,847</div>
                        </div>
                        
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-id">#20240518005</div>
                                <div class="order-time">10:30</div>
                            </div>
                            <div class="order-customer">刘同学</div>
                            <div class="order-merchant">美味小厨</div>
                            <div class="order-amount">¥32.50</div>
                        </div>
                    </div>
                    
                    <div class="status-column cancelled">
                        <div class="column-header">
                            <div class="column-title">已取消</div>
                            <div class="column-count">15</div>
                        </div>
                        
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-id">#20240518006</div>
                                <div class="order-time">09:45</div>
                            </div>
                            <div class="order-customer">陈同学</div>
                            <div class="order-merchant">星空奶茶</div>
                            <div class="order-amount">¥16.00</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
