<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家分类管理 - 校园外卖管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #001529 0%, #002140 100%);
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 18px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: #ff6b35;
        }
        
        .menu-item.active {
            color: white;
            background-color: rgba(255, 107, 53, 0.2);
            border-left-color: #ff6b35;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item {
            color: #ff6b35;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            border-radius: 8px;
        }
        
        .header-icon:hover {
            color: #ff6b35;
            background-color: #f5f5f5;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .page-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }
        
        .category-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
            transition: all 0.3s;
            position: relative;
        }
        
        .category-card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .category-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .category-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .category-icon.food {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
        }
        
        .category-icon.convenience {
            background: linear-gradient(135deg, #52c41a, #389e0d);
        }
        
        .category-icon.fruit {
            background: linear-gradient(135deg, #fa8c16, #d46b08);
        }
        
        .category-icon.drink {
            background: linear-gradient(135deg, #1890ff, #096dd9);
        }
        
        .category-icon.snack {
            background: linear-gradient(135deg, #722ed1, #531dab);
        }
        
        .category-icon.medicine {
            background: linear-gradient(135deg, #eb2f96, #c41d7f);
        }
        
        .category-info {
            flex: 1;
        }
        
        .category-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .category-description {
            font-size: 14px;
            color: #666;
        }
        
        .category-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .category-merchants {
            margin-bottom: 20px;
        }
        
        .merchants-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .merchant-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .merchant-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: #fafafa;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .merchant-avatar {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #666;
        }
        
        .merchant-name {
            flex: 1;
            color: #333;
        }
        
        .merchant-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
        }
        
        .status-active {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .status-pending {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .category-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }
        
        .action-btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }
        
        .action-btn.edit {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .action-btn.delete {
            background: #fff1f0;
            color: #ff4d4f;
        }
        
        .action-btn.manage {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .add-category-card {
            background: #fafafa;
            border: 2px dashed #d9d9d9;
            border-radius: 12px;
            padding: 40px 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .add-category-card:hover {
            border-color: #ff6b35;
            background: #fff7f0;
        }
        
        .add-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 24px;
            color: #999;
            transition: all 0.3s;
        }
        
        .add-category-card:hover .add-icon {
            background: #ff6b35;
            color: white;
        }
        
        .add-text {
            font-size: 16px;
            font-weight: 500;
            color: #666;
            margin-bottom: 8px;
        }
        
        .add-description {
            font-size: 14px;
            color: #999;
        }
        
        .category-card-menu {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: all 0.3s;
        }
        
        .category-card:hover .category-card-menu {
            opacity: 1;
        }
        
        .category-card-menu:hover {
            background: #e6e6e6;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="sidebar-title">校园外卖系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-users menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-truck menu-icon"></i>
                    <span>配送管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-line menu-icon"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bullhorn menu-icon"></i>
                    <span>营销管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="breadcrumb">
                        <span>商家管理</span> / <span class="breadcrumb-item">商家分类管理</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">系统管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">商家分类管理</h1>
                    <div class="page-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            新增分类
                        </button>
                    </div>
                </div>
                
                <div class="category-grid">
                    <div class="category-card">
                        <div class="category-card-menu">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                        <div class="category-header">
                            <div class="category-icon food">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-name">餐饮美食</div>
                                <div class="category-description">各类餐厅、小吃、快餐</div>
                            </div>
                        </div>
                        
                        <div class="category-stats">
                            <div class="stat-item">
                                <div class="stat-value">68</div>
                                <div class="stat-label">商家数量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">1,248</div>
                                <div class="stat-label">月订单</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.6</div>
                                <div class="stat-label">平均评分</div>
                            </div>
                        </div>
                        
                        <div class="category-merchants">
                            <div class="merchants-title">热门商家</div>
                            <div class="merchant-list">
                                <div class="merchant-item">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-utensils"></i>
                                    </div>
                                    <div class="merchant-name">美味小厨</div>
                                    <div class="merchant-status status-active">营业中</div>
                                </div>
                                <div class="merchant-item">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-hamburger"></i>
                                    </div>
                                    <div class="merchant-name">快乐汉堡</div>
                                    <div class="merchant-status status-active">营业中</div>
                                </div>
                                <div class="merchant-item">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-pizza-slice"></i>
                                    </div>
                                    <div class="merchant-name">意式披萨</div>
                                    <div class="merchant-status status-pending">待审核</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="category-actions">
                            <button class="action-btn manage">
                                <i class="fas fa-cog"></i>
                                管理
                            </button>
                            <button class="action-btn edit">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                        </div>
                    </div>
                    
                    <div class="category-card">
                        <div class="category-card-menu">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                        <div class="category-header">
                            <div class="category-icon convenience">
                                <i class="fas fa-shopping-basket"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-name">便利店</div>
                                <div class="category-description">日用品、零食、饮料</div>
                            </div>
                        </div>
                        
                        <div class="category-stats">
                            <div class="stat-item">
                                <div class="stat-value">24</div>
                                <div class="stat-label">商家数量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">856</div>
                                <div class="stat-label">月订单</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.3</div>
                                <div class="stat-label">平均评分</div>
                            </div>
                        </div>
                        
                        <div class="category-merchants">
                            <div class="merchants-title">热门商家</div>
                            <div class="merchant-list">
                                <div class="merchant-item">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-store"></i>
                                    </div>
                                    <div class="merchant-name">校园便利店</div>
                                    <div class="merchant-status status-pending">待审核</div>
                                </div>
                                <div class="merchant-item">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div class="merchant-name">24小时便利</div>
                                    <div class="merchant-status status-active">营业中</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="category-actions">
                            <button class="action-btn manage">
                                <i class="fas fa-cog"></i>
                                管理
                            </button>
                            <button class="action-btn edit">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                        </div>
                    </div>
                    
                    <div class="category-card">
                        <div class="category-card-menu">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                        <div class="category-header">
                            <div class="category-icon fruit">
                                <i class="fas fa-apple-alt"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-name">水果生鲜</div>
                                <div class="category-description">新鲜水果、蔬菜</div>
                            </div>
                        </div>
                        
                        <div class="category-stats">
                            <div class="stat-item">
                                <div class="stat-value">18</div>
                                <div class="stat-label">商家数量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">642</div>
                                <div class="stat-label">月订单</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.8</div>
                                <div class="stat-label">平均评分</div>
                            </div>
                        </div>
                        
                        <div class="category-merchants">
                            <div class="merchants-title">热门商家</div>
                            <div class="merchant-list">
                                <div class="merchant-item">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-apple-alt"></i>
                                    </div>
                                    <div class="merchant-name">新鲜果园</div>
                                    <div class="merchant-status status-active">营业中</div>
                                </div>
                                <div class="merchant-item">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-carrot"></i>
                                    </div>
                                    <div class="merchant-name">绿色蔬菜</div>
                                    <div class="merchant-status status-active">营业中</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="category-actions">
                            <button class="action-btn manage">
                                <i class="fas fa-cog"></i>
                                管理
                            </button>
                            <button class="action-btn edit">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                        </div>
                    </div>
                    
                    <div class="category-card">
                        <div class="category-card-menu">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                        <div class="category-header">
                            <div class="category-icon drink">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-name">饮品甜品</div>
                                <div class="category-description">奶茶、咖啡、甜品</div>
                            </div>
                        </div>
                        
                        <div class="category-stats">
                            <div class="stat-item">
                                <div class="stat-value">32</div>
                                <div class="stat-label">商家数量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">1,156</div>
                                <div class="stat-label">月订单</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.4</div>
                                <div class="stat-label">平均评分</div>
                            </div>
                        </div>
                        
                        <div class="category-merchants">
                            <div class="merchants-title">热门商家</div>
                            <div class="merchant-list">
                                <div class="merchant-item">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-coffee"></i>
                                    </div>
                                    <div class="merchant-name">星空奶茶</div>
                                    <div class="merchant-status status-active">营业中</div>
                                </div>
                                <div class="merchant-item">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-ice-cream"></i>
                                    </div>
                                    <div class="merchant-name">甜蜜时光</div>
                                    <div class="merchant-status status-active">营业中</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="category-actions">
                            <button class="action-btn manage">
                                <i class="fas fa-cog"></i>
                                管理
                            </button>
                            <button class="action-btn edit">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                        </div>
                    </div>
                    
                    <div class="category-card">
                        <div class="category-card-menu">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                        <div class="category-header">
                            <div class="category-icon snack">
                                <i class="fas fa-cookie-bite"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-name">零食小食</div>
                                <div class="category-description">各类零食、小食</div>
                            </div>
                        </div>
                        
                        <div class="category-stats">
                            <div class="stat-item">
                                <div class="stat-value">14</div>
                                <div class="stat-label">商家数量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">428</div>
                                <div class="stat-label">月订单</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.2</div>
                                <div class="stat-label">平均评分</div>
                            </div>
                        </div>
                        
                        <div class="category-merchants">
                            <div class="merchants-title">热门商家</div>
                            <div class="merchant-list">
                                <div class="merchant-item">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-cookie"></i>
                                    </div>
                                    <div class="merchant-name">零食王国</div>
                                    <div class="merchant-status status-active">营业中</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="category-actions">
                            <button class="action-btn manage">
                                <i class="fas fa-cog"></i>
                                管理
                            </button>
                            <button class="action-btn edit">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                        </div>
                    </div>
                    
                    <div class="add-category-card">
                        <div class="add-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="add-text">添加新分类</div>
                        <div class="add-description">创建新的商家分类</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
