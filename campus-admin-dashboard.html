<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校园外卖管理系统 - 仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #001529 0%, #002140 100%);
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 18px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: #ff6b35;
        }
        
        .menu-item.active {
            color: white;
            background-color: rgba(255, 107, 53, 0.2);
            border-left-color: #ff6b35;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item {
            color: #ff6b35;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            border-radius: 8px;
        }
        
        .header-icon:hover {
            color: #ff6b35;
            background-color: #f5f5f5;
        }
        
        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background-color: #ff4d4f;
            border-radius: 50%;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .user-info:hover {
            background-color: #f5f5f5;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .page-description {
            font-size: 14px;
            color: #666;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
            transition: all 0.3s;
        }
        
        .stat-card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .stat-title {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }
        
        .stat-icon.orders {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
        }
        
        .stat-icon.merchants {
            background: linear-gradient(135deg, #52c41a, #389e0d);
        }
        
        .stat-icon.users {
            background: linear-gradient(135deg, #1890ff, #096dd9);
        }
        
        .stat-icon.revenue {
            background: linear-gradient(135deg, #722ed1, #531dab);
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }
        
        .stat-trend {
            display: flex;
            align-items: center;
            font-size: 12px;
        }
        
        .trend-up {
            color: #52c41a;
        }
        
        .trend-down {
            color: #ff4d4f;
        }
        
        .trend-icon {
            margin-right: 4px;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .chart-actions {
            display: flex;
            align-items: center;
        }
        
        .chart-action {
            color: #666;
            font-size: 14px;
            margin-left: 16px;
            cursor: pointer;
            transition: all 0.3s;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .chart-action:hover {
            color: #ff6b35;
            background-color: #fff7f0;
        }
        
        .chart-action.active {
            color: #ff6b35;
            background-color: #fff7f0;
        }
        
        .chart-content {
            height: 280px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #bfbfbf;
            background: #fafafa;
            border-radius: 8px;
        }
        
        .recent-activities {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
        }
        
        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .activity-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            color: white;
        }
        
        .activity-icon.new-order {
            background: #ff6b35;
        }
        
        .activity-icon.new-merchant {
            background: #52c41a;
        }
        
        .activity-icon.user-register {
            background: #1890ff;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="sidebar-title">校园外卖系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item active">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-users menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-truck menu-icon"></i>
                    <span>配送管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-line menu-icon"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bullhorn menu-icon"></i>
                    <span>营销管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">仪表盘</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">8</div>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">系统管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">数据概览</h1>
                    <p class="page-description">欢迎回来，这里是您的校园外卖平台运营数据</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">今日订单</div>
                            <div class="stat-icon orders">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                        </div>
                        <div class="stat-value">2,847</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>较昨日 +18.2%</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">活跃商家</div>
                            <div class="stat-icon merchants">
                                <i class="fas fa-store"></i>
                            </div>
                        </div>
                        <div class="stat-value">156</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>较昨日 +5.3%</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">注册用户</div>
                            <div class="stat-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value">12,486</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>较昨日 +2.8%</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">今日营收</div>
                            <div class="stat-icon revenue">
                                <i class="fas fa-yen-sign"></i>
                            </div>
                        </div>
                        <div class="stat-value">¥68,924</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up trend-icon"></i>
                            <span>较昨日 +12.5%</span>
                        </div>
                    </div>
                </div>
                
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">订单趋势分析</div>
                            <div class="chart-actions">
                                <span class="chart-action active">今日</span>
                                <span class="chart-action">本周</span>
                                <span class="chart-action">本月</span>
                            </div>
                        </div>
                        <div class="chart-content">
                            <i class="fas fa-chart-line" style="font-size: 80px;"></i>
                        </div>
                    </div>
                    
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">商家类型分布</div>
                            <div class="chart-actions">
                                <i class="fas fa-ellipsis-v chart-action"></i>
                            </div>
                        </div>
                        <div class="chart-content">
                            <i class="fas fa-chart-pie" style="font-size: 80px;"></i>
                        </div>
                    </div>
                </div>
                
                <div class="recent-activities">
                    <div class="activity-header">
                        <div class="activity-title">实时动态</div>
                        <a href="#" style="color: #ff6b35; font-size: 14px;">查看全部</a>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon new-order">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">新订单：学生张三在"美味小厨"下单，订单金额 ¥28.5</div>
                            <div class="activity-time">2分钟前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon new-merchant">
                            <i class="fas fa-store"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">新商家："校园便利店"提交入驻申请，等待审核</div>
                            <div class="activity-time">15分钟前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon user-register">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">新用户注册：计算机学院学生李四完成注册</div>
                            <div class="activity-time">32分钟前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon new-order">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">配送完成：订单#20240518001已送达宿舍楼A栋</div>
                            <div class="activity-time">45分钟前</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
