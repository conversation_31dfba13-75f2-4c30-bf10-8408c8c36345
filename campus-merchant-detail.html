<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家详情 - 校园外卖管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #001529 0%, #002140 100%);
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 18px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: #ff6b35;
        }
        
        .menu-item.active {
            color: white;
            background-color: rgba(255, 107, 53, 0.2);
            border-left-color: #ff6b35;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item {
            color: #ff6b35;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            border-radius: 8px;
        }
        
        .header-icon:hover {
            color: #ff6b35;
            background-color: #f5f5f5;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .page-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }
        
        .btn-secondary {
            background: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-danger {
            background: #ff4d4f;
            color: white;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .detail-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .card-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .merchant-profile {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .merchant-logo {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: #666;
        }
        
        .merchant-info {
            flex: 1;
        }
        
        .merchant-name {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .merchant-category {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .merchant-rating {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .rating-stars {
            color: #faad14;
        }
        
        .status-tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .info-label {
            font-size: 12px;
            color: #999;
            font-weight: 500;
        }
        
        .info-value {
            font-size: 14px;
            color: #333;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }
        
        .stat-item {
            text-align: center;
            padding: 16px;
            background: #fafafa;
            border-radius: 8px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .full-width-card {
            grid-column: 1 / -1;
        }
        
        .product-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
        }
        
        .product-item {
            background: #fafafa;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        
        .product-image {
            width: 60px;
            height: 60px;
            background: #e0e0e0;
            border-radius: 8px;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #999;
        }
        
        .product-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .product-price {
            font-size: 16px;
            font-weight: 600;
            color: #ff6b35;
        }
        
        .order-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: #fafafa;
            border-radius: 8px;
        }
        
        .order-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .order-id {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .order-time {
            font-size: 12px;
            color: #999;
        }
        
        .order-amount {
            font-size: 16px;
            font-weight: 600;
            color: #ff6b35;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="sidebar-title">校园外卖系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-users menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-truck menu-icon"></i>
                    <span>配送管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-line menu-icon"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bullhorn menu-icon"></i>
                    <span>营销管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="breadcrumb">
                        <span>商家管理</span> / <span>商家列表</span> / <span class="breadcrumb-item">商家详情</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">系统管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">商家详情</h1>
                    <div class="page-actions">
                        <button class="btn btn-secondary">
                            <i class="fas fa-edit"></i>
                            编辑信息
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-check"></i>
                            审核通过
                        </button>
                        <button class="btn btn-danger">
                            <i class="fas fa-ban"></i>
                            暂停营业
                        </button>
                    </div>
                </div>
                
                <div class="detail-grid">
                    <div class="detail-card">
                        <div class="card-title">
                            <div class="card-icon">
                                <i class="fas fa-store"></i>
                            </div>
                            基本信息
                        </div>
                        
                        <div class="merchant-profile">
                            <div class="merchant-logo">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="merchant-info">
                                <div class="merchant-name">美味小厨</div>
                                <div class="merchant-category">餐饮美食 · 川菜湘菜</div>
                                <div class="merchant-rating">
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                    </div>
                                    <span>4.5分 (1,248条评价)</span>
                                    <span class="status-tag status-active">营业中</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">商家ID</div>
                                <div class="info-value">MER20240315001</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">联系人</div>
                                <div class="info-value">张老板</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">联系电话</div>
                                <div class="info-value">13812345678</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">营业时间</div>
                                <div class="info-value">08:00 - 22:00</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">商家地址</div>
                                <div class="info-value">学生食堂二楼A区</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">入驻时间</div>
                                <div class="info-value">2024-03-15</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-card">
                        <div class="card-title">
                            <div class="card-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            经营数据
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">1,248</div>
                                <div class="stat-label">月销量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">¥28,456</div>
                                <div class="stat-label">月营收</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.5</div>
                                <div class="stat-label">平均评分</div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <div class="info-grid">
                                <div class="info-item">
                                    <div class="info-label">配送费</div>
                                    <div class="info-value">¥3.00</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">起送金额</div>
                                    <div class="info-value">¥15.00</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">平均配送时间</div>
                                    <div class="info-value">25分钟</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">佣金比例</div>
                                    <div class="info-value">8%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-card full-width-card">
                        <div class="card-title">
                            <div class="card-icon">
                                <i class="fas fa-utensils"></i>
                            </div>
                            热门商品
                        </div>
                        
                        <div class="product-list">
                            <div class="product-item">
                                <div class="product-image">
                                    <i class="fas fa-pepper-hot"></i>
                                </div>
                                <div class="product-name">麻辣香锅</div>
                                <div class="product-price">¥18.00</div>
                            </div>
                            <div class="product-item">
                                <div class="product-image">
                                    <i class="fas fa-drumstick-bite"></i>
                                </div>
                                <div class="product-name">口水鸡</div>
                                <div class="product-price">¥15.00</div>
                            </div>
                            <div class="product-item">
                                <div class="product-image">
                                    <i class="fas fa-fish"></i>
                                </div>
                                <div class="product-name">水煮鱼</div>
                                <div class="product-price">¥28.00</div>
                            </div>
                            <div class="product-item">
                                <div class="product-image">
                                    <i class="fas fa-bowl-rice"></i>
                                </div>
                                <div class="product-name">蛋炒饭</div>
                                <div class="product-price">¥12.00</div>
                            </div>
                            <div class="product-item">
                                <div class="product-image">
                                    <i class="fas fa-seedling"></i>
                                </div>
                                <div class="product-name">青椒肉丝</div>
                                <div class="product-price">¥16.00</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-card full-width-card">
                        <div class="card-title">
                            <div class="card-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            最近订单
                        </div>
                        
                        <div class="order-list">
                            <div class="order-item">
                                <div class="order-info">
                                    <div class="order-id">订单 #20240518001</div>
                                    <div class="order-time">2024-05-18 12:30 · 张同学</div>
                                </div>
                                <div class="order-amount">¥28.50</div>
                            </div>
                            <div class="order-item">
                                <div class="order-info">
                                    <div class="order-id">订单 #20240518002</div>
                                    <div class="order-time">2024-05-18 11:45 · 李同学</div>
                                </div>
                                <div class="order-amount">¥35.00</div>
                            </div>
                            <div class="order-item">
                                <div class="order-info">
                                    <div class="order-id">订单 #20240518003</div>
                                    <div class="order-time">2024-05-18 11:20 · 王同学</div>
                                </div>
                                <div class="order-amount">¥22.00</div>
                            </div>
                            <div class="order-item">
                                <div class="order-info">
                                    <div class="order-id">订单 #20240518004</div>
                                    <div class="order-time">2024-05-18 10:55 · 赵同学</div>
                                </div>
                                <div class="order-amount">¥41.50</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
