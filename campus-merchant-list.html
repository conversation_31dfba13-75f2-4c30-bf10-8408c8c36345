<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家列表 - 校园外卖管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #001529 0%, #002140 100%);
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 18px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: #ff6b35;
        }
        
        .menu-item.active {
            color: white;
            background-color: rgba(255, 107, 53, 0.2);
            border-left-color: #ff6b35;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item {
            color: #ff6b35;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            border-radius: 8px;
        }
        
        .header-icon:hover {
            color: #ff6b35;
            background-color: #f5f5f5;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .page-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
        
        .btn-secondary {
            background: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-secondary:hover {
            color: #ff6b35;
            border-color: #ff6b35;
        }
        
        .filter-bar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .filter-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }
        
        .filter-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 160px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 120px;
            background: white;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .table-stats {
            font-size: 14px;
            color: #666;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #333;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .data-table td {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .data-table tr:hover {
            background: #fafafa;
        }
        
        .merchant-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .merchant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #666;
        }
        
        .merchant-details {
            display: flex;
            flex-direction: column;
        }
        
        .merchant-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }
        
        .merchant-category {
            font-size: 12px;
            color: #999;
        }
        
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-pending {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        .status-suspended {
            background: #fff1f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        .rating-stars {
            color: #faad14;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }
        
        .action-btn.view {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .action-btn.edit {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .action-btn.delete {
            background: #fff1f0;
            color: #ff4d4f;
        }
        
        .pagination {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }
        
        .pagination-info {
            font-size: 14px;
            color: #666;
        }
        
        .pagination-controls {
            display: flex;
            gap: 8px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .page-btn:hover {
            color: #ff6b35;
            border-color: #ff6b35;
        }
        
        .page-btn.active {
            background: #ff6b35;
            color: white;
            border-color: #ff6b35;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="sidebar-title">校园外卖系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-users menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-truck menu-icon"></i>
                    <span>配送管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-line menu-icon"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bullhorn menu-icon"></i>
                    <span>营销管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="breadcrumb">
                        <span>商家管理</span> / <span class="breadcrumb-item">商家列表</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">系统管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">商家列表</h1>
                    <div class="page-actions">
                        <button class="btn btn-secondary">
                            <i class="fas fa-download"></i>
                            导出数据
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            添加商家
                        </button>
                    </div>
                </div>
                
                <div class="filter-bar">
                    <div class="filter-item">
                        <label class="filter-label">商家名称</label>
                        <input type="text" class="filter-input" placeholder="搜索商家名称">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">商家类型</label>
                        <select class="filter-select">
                            <option>全部类型</option>
                            <option>餐饮美食</option>
                            <option>便利店</option>
                            <option>水果生鲜</option>
                            <option>饮品甜品</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">状态</label>
                        <select class="filter-select">
                            <option>全部状态</option>
                            <option>营业中</option>
                            <option>待审核</option>
                            <option>已暂停</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">入驻时间</label>
                        <input type="date" class="filter-input">
                    </div>
                    <button class="btn btn-primary" style="margin-top: 20px;">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                    <button class="btn btn-secondary" style="margin-top: 20px;">
                        <i class="fas fa-redo"></i>
                        重置
                    </button>
                </div>
                
                <div class="table-container">
                    <div class="table-header">
                        <div class="table-title">商家列表</div>
                        <div class="table-stats">共 156 家商家</div>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>商家信息</th>
                                <th>联系方式</th>
                                <th>商家类型</th>
                                <th>评分</th>
                                <th>月销量</th>
                                <th>状态</th>
                                <th>入驻时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-utensils"></i>
                                        </div>
                                        <div class="merchant-details">
                                            <div class="merchant-name">美味小厨</div>
                                            <div class="merchant-category">主营：川菜、湘菜</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>张老板</div>
                                    <div style="color: #999; font-size: 12px;">13812345678</div>
                                </td>
                                <td>餐饮美食</td>
                                <td>
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                        <span style="color: #333; margin-left: 4px;">4.5</span>
                                    </div>
                                </td>
                                <td>1,248</td>
                                <td><span class="status-tag status-active">营业中</span></td>
                                <td>2024-03-15</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn edit">编辑</button>
                                        <button class="action-btn delete">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-shopping-basket"></i>
                                        </div>
                                        <div class="merchant-details">
                                            <div class="merchant-name">校园便利店</div>
                                            <div class="merchant-category">主营：零食、饮料</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>李经理</div>
                                    <div style="color: #999; font-size: 12px;">13987654321</div>
                                </td>
                                <td>便利店</td>
                                <td>
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <span style="color: #333; margin-left: 4px;">4.2</span>
                                    </div>
                                </td>
                                <td>856</td>
                                <td><span class="status-tag status-pending">待审核</span></td>
                                <td>2024-05-18</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn edit">编辑</button>
                                        <button class="action-btn delete">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-apple-alt"></i>
                                        </div>
                                        <div class="merchant-details">
                                            <div class="merchant-name">新鲜果园</div>
                                            <div class="merchant-category">主营：水果、蔬菜</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>王阿姨</div>
                                    <div style="color: #999; font-size: 12px;">13765432198</div>
                                </td>
                                <td>水果生鲜</td>
                                <td>
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <span style="color: #333; margin-left: 4px;">4.8</span>
                                    </div>
                                </td>
                                <td>642</td>
                                <td><span class="status-tag status-active">营业中</span></td>
                                <td>2024-02-28</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn edit">编辑</button>
                                        <button class="action-btn delete">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-coffee"></i>
                                        </div>
                                        <div class="merchant-details">
                                            <div class="merchant-name">星空奶茶</div>
                                            <div class="merchant-category">主营：奶茶、咖啡</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>小刘</div>
                                    <div style="color: #999; font-size: 12px;">13612345678</div>
                                </td>
                                <td>饮品甜品</td>
                                <td>
                                    <div class="rating-stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <span style="color: #333; margin-left: 4px;">4.1</span>
                                    </div>
                                </td>
                                <td>1,156</td>
                                <td><span class="status-tag status-suspended">已暂停</span></td>
                                <td>2024-01-12</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn edit">编辑</button>
                                        <button class="action-btn delete">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination">
                        <div class="pagination-info">
                            显示 1-10 条，共 156 条记录
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn">上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">...</button>
                            <button class="page-btn">16</button>
                            <button class="page-btn">下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
