<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校园外卖后台管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f0f2f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .prototype-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            padding: 30px;
        }
        .desktop-frame {
            width: 100%;
            height: 900px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            position: relative;
            margin: 0 auto;
            border: 1px solid #ddd;
        }
        .screen {
            width: 100%;
            height: 100%;
            border: none;
        }
        .screen-title {
            text-align: center;
            font-weight: bold;
            margin-top: 15px;
            margin-bottom: 10px;
            font-size: 18px;
            color: #333;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            padding: 1rem 0;
        }
        .module-section {
            margin-bottom: 3rem;
        }
        .module-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1.5rem;
            padding-left: 1rem;
            border-left: 4px solid #ff6b35;
        }
    </style>
</head>
<body>
    <div class="container mx-auto py-8">
        <h1 class="page-title">校园外卖后台管理系统原型图</h1>

        <!-- 系统登录与仪表盘 -->
        <div class="module-section">
            <h2 class="module-title">系统登录与仪表盘</h2>
            <div class="prototype-container">
                <div>
                    <div class="screen-title">管理员登录</div>
                    <div class="desktop-frame">
                        <iframe src="campus-admin-login.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">管理员仪表盘</div>
                    <div class="desktop-frame">
                        <iframe src="campus-admin-dashboard.html" class="screen"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商家管理模块 -->
        <div class="module-section">
            <h2 class="module-title">商家管理模块</h2>
            <div class="prototype-container">
                <div>
                    <div class="screen-title">商家列表</div>
                    <div class="desktop-frame">
                        <iframe src="campus-merchant-list.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">商家详情</div>
                    <div class="desktop-frame">
                        <iframe src="campus-merchant-detail.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">商家审核</div>
                    <div class="desktop-frame">
                        <iframe src="campus-merchant-verification.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">商家分类管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-merchant-category.html" class="screen"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单管理模块 -->
        <div class="module-section">
            <h2 class="module-title">订单管理模块</h2>
            <div class="prototype-container">
                <div>
                    <div class="screen-title">订单列表</div>
                    <div class="desktop-frame">
                        <iframe src="campus-order-list.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">订单详情</div>
                    <div class="desktop-frame">
                        <iframe src="campus-order-detail.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">订单状态管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-order-status.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">退款管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-refund-management.html" class="screen"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户管理模块 -->
        <div class="module-section">
            <h2 class="module-title">用户管理模块</h2>
            <div class="prototype-container">
                <div>
                    <div class="screen-title">用户列表</div>
                    <div class="desktop-frame">
                        <iframe src="campus-user-list.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">用户详情</div>
                    <div class="desktop-frame">
                        <iframe src="campus-user-detail.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">用户行为分析</div>
                    <div class="desktop-frame">
                        <iframe src="campus-user-analytics.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">学生认证管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-student-verification.html" class="screen"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配送管理模块 -->
        <div class="module-section">
            <h2 class="module-title">配送管理模块</h2>
            <div class="prototype-container">
                <div>
                    <div class="screen-title">配送员管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-delivery-staff.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">配送区域设置</div>
                    <div class="desktop-frame">
                        <iframe src="campus-delivery-area.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">配送费用配置</div>
                    <div class="desktop-frame">
                        <iframe src="campus-delivery-fee.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">配送实时监控</div>
                    <div class="desktop-frame">
                        <iframe src="campus-delivery-monitor.html" class="screen"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- 财务管理模块 -->
        <div class="module-section">
            <h2 class="module-title">财务管理模块</h2>
            <div class="prototype-container">
                <div>
                    <div class="screen-title">交易流水</div>
                    <div class="desktop-frame">
                        <iframe src="campus-transaction-log.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">结算管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-settlement.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">财务报表</div>
                    <div class="desktop-frame">
                        <iframe src="campus-financial-report.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">佣金管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-commission.html" class="screen"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- 营销管理模块 -->
        <div class="module-section">
            <h2 class="module-title">营销管理模块</h2>
            <div class="prototype-container">
                <div>
                    <div class="screen-title">优惠券管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-coupon-management.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">活动管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-activity-management.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">推广管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-promotion-management.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">积分管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-points-management.html" class="screen"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统管理模块 -->
        <div class="module-section">
            <h2 class="module-title">系统管理模块</h2>
            <div class="prototype-container">
                <div>
                    <div class="screen-title">系统设置</div>
                    <div class="desktop-frame">
                        <iframe src="campus-system-settings.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">权限管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-permission-management.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">日志管理</div>
                    <div class="desktop-frame">
                        <iframe src="campus-log-management.html" class="screen"></iframe>
                    </div>
                </div>
                <div>
                    <div class="screen-title">数据统计</div>
                    <div class="desktop-frame">
                        <iframe src="campus-data-statistics.html" class="screen"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
