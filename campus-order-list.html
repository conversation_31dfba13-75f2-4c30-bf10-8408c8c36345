<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单列表 - 校园外卖管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .admin-layout {
            display: flex;
            height: 100vh;
            width: 1440px;
        }
        
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #001529 0%, #002140 100%);
            color: white;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-size: 18px;
        }
        
        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .sidebar-menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: #ff6b35;
        }
        
        .menu-item.active {
            color: white;
            background-color: rgba(255, 107, 53, 0.2);
            border-left-color: #ff6b35;
        }
        
        .menu-icon {
            font-size: 16px;
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb-item {
            color: #ff6b35;
            font-weight: 500;
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            border-radius: 8px;
        }
        
        .header-icon:hover {
            color: #ff6b35;
            background-color: #f5f5f5;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-left: 16px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .stats-bar {
            display: flex;
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-item {
            background: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 140px;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }
        
        .stat-icon.pending {
            background: linear-gradient(135deg, #fa8c16, #d46b08);
        }
        
        .stat-icon.processing {
            background: linear-gradient(135deg, #1890ff, #096dd9);
        }
        
        .stat-icon.completed {
            background: linear-gradient(135deg, #52c41a, #389e0d);
        }
        
        .stat-icon.cancelled {
            background: linear-gradient(135deg, #ff4d4f, #cf1322);
        }
        
        .stat-content {
            display: flex;
            flex-direction: column;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .filter-bar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .filter-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }
        
        .filter-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 160px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 120px;
            background: white;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }
        
        .btn-secondary {
            background: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .table-stats {
            font-size: 14px;
            color: #666;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #333;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .data-table td {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .data-table tr:hover {
            background: #fafafa;
        }
        
        .order-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .order-id {
            font-weight: 500;
            color: #333;
        }
        
        .order-time {
            font-size: 12px;
            color: #999;
        }
        
        .customer-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .customer-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
        }
        
        .customer-details {
            display: flex;
            flex-direction: column;
        }
        
        .customer-name {
            font-weight: 500;
            color: #333;
        }
        
        .customer-phone {
            font-size: 12px;
            color: #999;
        }
        
        .merchant-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .merchant-avatar {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
        }
        
        .merchant-name {
            font-weight: 500;
            color: #333;
        }
        
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        .status-processing {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .status-delivering {
            background: #f0f5ff;
            color: #722ed1;
            border: 1px solid #d3adf7;
        }
        
        .status-completed {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-cancelled {
            background: #fff1f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        .amount-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }
        
        .amount-total {
            font-size: 16px;
            font-weight: 600;
            color: #ff6b35;
        }
        
        .amount-detail {
            font-size: 12px;
            color: #999;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }
        
        .action-btn.view {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .action-btn.process {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .action-btn.cancel {
            background: #fff1f0;
            color: #ff4d4f;
        }
        
        .pagination {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }
        
        .pagination-info {
            font-size: 14px;
            color: #666;
        }
        
        .pagination-controls {
            display: flex;
            gap: 8px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .page-btn:hover {
            color: #ff6b35;
            border-color: #ff6b35;
        }
        
        .page-btn.active {
            background: #ff6b35;
            color: white;
            border-color: #ff6b35;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="sidebar-title">校园外卖系统</div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-item">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-store menu-icon"></i>
                    <span>商家管理</span>
                </div>
                <div class="menu-item active">
                    <i class="fas fa-shopping-cart menu-icon"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-users menu-icon"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-truck menu-icon"></i>
                    <span>配送管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-chart-line menu-icon"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-bullhorn menu-icon"></i>
                    <span>营销管理</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-cog menu-icon"></i>
                    <span>系统管理</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <div class="breadcrumb">
                        <span>订单管理</span> / <span class="breadcrumb-item">订单列表</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>管</span>
                        </div>
                        <div class="user-name">系统管理员</div>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">订单列表</h1>
                </div>
                
                <div class="stats-bar">
                    <div class="stat-item">
                        <div class="stat-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">42</div>
                            <div class="stat-label">待处理</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon processing">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">128</div>
                            <div class="stat-label">制作中</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon delivering">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">86</div>
                            <div class="stat-label">配送中</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon completed">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">2,847</div>
                            <div class="stat-label">已完成</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon cancelled">
                            <i class="fas fa-times"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">15</div>
                            <div class="stat-label">已取消</div>
                        </div>
                    </div>
                </div>
                
                <div class="filter-bar">
                    <div class="filter-item">
                        <label class="filter-label">订单号</label>
                        <input type="text" class="filter-input" placeholder="搜索订单号">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">订单状态</label>
                        <select class="filter-select">
                            <option>全部状态</option>
                            <option>待处理</option>
                            <option>制作中</option>
                            <option>配送中</option>
                            <option>已完成</option>
                            <option>已取消</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">商家</label>
                        <select class="filter-select">
                            <option>全部商家</option>
                            <option>美味小厨</option>
                            <option>校园便利店</option>
                            <option>新鲜果园</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">下单时间</label>
                        <input type="date" class="filter-input">
                    </div>
                    <button class="btn btn-primary" style="margin-top: 20px;">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                    <button class="btn btn-secondary" style="margin-top: 20px;">
                        <i class="fas fa-redo"></i>
                        重置
                    </button>
                </div>
                
                <div class="table-container">
                    <div class="table-header">
                        <div class="table-title">订单列表</div>
                        <div class="table-stats">共 3,118 个订单</div>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>订单信息</th>
                                <th>客户信息</th>
                                <th>商家</th>
                                <th>订单状态</th>
                                <th>配送地址</th>
                                <th>订单金额</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="order-info">
                                        <div class="order-id">#20240518001</div>
                                        <div class="order-time">2024-05-18 12:30</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="customer-info">
                                        <div class="customer-avatar">张</div>
                                        <div class="customer-details">
                                            <div class="customer-name">张同学</div>
                                            <div class="customer-phone">138****5678</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-utensils"></i>
                                        </div>
                                        <div class="merchant-name">美味小厨</div>
                                    </div>
                                </td>
                                <td><span class="status-tag status-processing">制作中</span></td>
                                <td>学生宿舍A栋201</td>
                                <td>
                                    <div class="amount-info">
                                        <div class="amount-total">¥28.50</div>
                                        <div class="amount-detail">含配送费¥3</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn process">处理</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="order-info">
                                        <div class="order-id">#20240518002</div>
                                        <div class="order-time">2024-05-18 11:45</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="customer-info">
                                        <div class="customer-avatar">李</div>
                                        <div class="customer-details">
                                            <div class="customer-name">李同学</div>
                                            <div class="customer-phone">139****4321</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-shopping-basket"></i>
                                        </div>
                                        <div class="merchant-name">校园便利店</div>
                                    </div>
                                </td>
                                <td><span class="status-tag status-delivering">配送中</span></td>
                                <td>学生宿舍B栋305</td>
                                <td>
                                    <div class="amount-info">
                                        <div class="amount-total">¥15.80</div>
                                        <div class="amount-detail">含配送费¥2</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn process">处理</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="order-info">
                                        <div class="order-id">#20240518003</div>
                                        <div class="order-time">2024-05-18 11:20</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="customer-info">
                                        <div class="customer-avatar">王</div>
                                        <div class="customer-details">
                                            <div class="customer-name">王同学</div>
                                            <div class="customer-phone">137****2198</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-apple-alt"></i>
                                        </div>
                                        <div class="merchant-name">新鲜果园</div>
                                    </div>
                                </td>
                                <td><span class="status-tag status-completed">已完成</span></td>
                                <td>教学楼C栋办公室</td>
                                <td>
                                    <div class="amount-info">
                                        <div class="amount-total">¥22.00</div>
                                        <div class="amount-detail">含配送费¥3</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="order-info">
                                        <div class="order-id">#20240518004</div>
                                        <div class="order-time">2024-05-18 10:55</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="customer-info">
                                        <div class="customer-avatar">赵</div>
                                        <div class="customer-details">
                                            <div class="customer-name">赵同学</div>
                                            <div class="customer-phone">136****5678</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-coffee"></i>
                                        </div>
                                        <div class="merchant-name">星空奶茶</div>
                                    </div>
                                </td>
                                <td><span class="status-tag status-pending">待处理</span></td>
                                <td>图书馆三楼阅览室</td>
                                <td>
                                    <div class="amount-info">
                                        <div class="amount-total">¥18.00</div>
                                        <div class="amount-detail">含配送费¥2</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                        <button class="action-btn process">处理</button>
                                        <button class="action-btn cancel">取消</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="order-info">
                                        <div class="order-id">#20240518005</div>
                                        <div class="order-time">2024-05-18 10:30</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="customer-info">
                                        <div class="customer-avatar">刘</div>
                                        <div class="customer-details">
                                            <div class="customer-name">刘同学</div>
                                            <div class="customer-phone">135****9876</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="merchant-info">
                                        <div class="merchant-avatar">
                                            <i class="fas fa-utensils"></i>
                                        </div>
                                        <div class="merchant-name">美味小厨</div>
                                    </div>
                                </td>
                                <td><span class="status-tag status-cancelled">已取消</span></td>
                                <td>学生宿舍D栋108</td>
                                <td>
                                    <div class="amount-info">
                                        <div class="amount-total">¥32.50</div>
                                        <div class="amount-detail">已退款</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view">查看</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination">
                        <div class="pagination-info">
                            显示 1-10 条，共 3,118 条记录
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn">上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">...</button>
                            <button class="page-btn">312</button>
                            <button class="page-btn">下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
